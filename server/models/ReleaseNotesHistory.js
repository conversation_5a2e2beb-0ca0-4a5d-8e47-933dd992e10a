const mongoose = require('mongoose');

const releaseNotesHistorySchema = new mongoose.Schema({
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  generatedAt: {
    type: Date,
    default: Date.now
  },
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  filters: {
    tags: [{
      type: String
    }],
    labels: [{
      type: String
    }],
    features: [{
      type: String
    }],
    states: [{
      type: String
    }]
  }
}, {
  timestamps: true
});

// Add indexes for performance
releaseNotesHistorySchema.index({ group: 1, project: 1, generatedAt: -1 });
releaseNotesHistorySchema.index({ generatedAt: -1 });

module.exports = mongoose.model('ReleaseNotesHistory', releaseNotesHistorySchema);
