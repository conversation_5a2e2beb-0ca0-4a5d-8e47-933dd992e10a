const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for migration');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Migration function to move tags from document level to current version
const migrateTags = async () => {
  try {
    console.log('Starting tag migration...');

    // Migrate Features
    console.log('Migrating Feature tags...');
    const features = await mongoose.connection.db.collection('features').find({
      releaseTags: { $exists: true, $ne: [] }
    }).toArray();

    console.log(`Found ${features.length} features with tags to migrate`);

    for (const feature of features) {
      if (feature.releaseTags && feature.releaseTags.length > 0) {
        // Find the current version
        const currentVersionNumber = feature.currentVersion || 1;
        const currentVersionIndex = feature.versions.findIndex(v => v.version === currentVersionNumber);
        
        if (currentVersionIndex !== -1) {
          // Move tags to current version
          feature.versions[currentVersionIndex].releaseTags = feature.releaseTags;
          
          // Update the document
          await mongoose.connection.db.collection('features').updateOne(
            { _id: feature._id },
            { 
              $set: { 
                [`versions.${currentVersionIndex}.releaseTags`]: feature.releaseTags 
              },
              $unset: { releaseTags: "" }
            }
          );
          
          console.log(`Migrated ${feature.releaseTags.length} tags for feature ${feature._id} to version ${currentVersionNumber}`);
        } else {
          console.warn(`Could not find current version ${currentVersionNumber} for feature ${feature._id}`);
        }
      }
    }

    // Migrate Requirements
    console.log('Migrating Requirement tags...');
    const requirements = await mongoose.connection.db.collection('requirements').find({
      releaseTags: { $exists: true, $ne: [] }
    }).toArray();

    console.log(`Found ${requirements.length} requirements with tags to migrate`);

    for (const requirement of requirements) {
      if (requirement.releaseTags && requirement.releaseTags.length > 0) {
        // Find the current version
        const currentVersionNumber = requirement.currentVersion || 1;
        const currentVersionIndex = requirement.versions.findIndex(v => v.version === currentVersionNumber);
        
        if (currentVersionIndex !== -1) {
          // Move tags to current version
          requirement.versions[currentVersionIndex].releaseTags = requirement.releaseTags;
          
          // Update the document
          await mongoose.connection.db.collection('requirements').updateOne(
            { _id: requirement._id },
            { 
              $set: { 
                [`versions.${currentVersionIndex}.releaseTags`]: requirement.releaseTags 
              },
              $unset: { releaseTags: "" }
            }
          );
          
          console.log(`Migrated ${requirement.releaseTags.length} tags for requirement ${requirement._id} to version ${currentVersionNumber}`);
        } else {
          console.warn(`Could not find current version ${currentVersionNumber} for requirement ${requirement._id}`);
        }
      }
    }

    console.log('Tag migration completed successfully!');
    
    // Verify migration
    const featuresWithOldTags = await mongoose.connection.db.collection('features').countDocuments({
      releaseTags: { $exists: true, $ne: [] }
    });
    
    const requirementsWithOldTags = await mongoose.connection.db.collection('requirements').countDocuments({
      releaseTags: { $exists: true, $ne: [] }
    });
    
    console.log(`Verification: ${featuresWithOldTags} features and ${requirementsWithOldTags} requirements still have document-level tags`);
    
    if (featuresWithOldTags === 0 && requirementsWithOldTags === 0) {
      console.log('✅ Migration successful - all tags moved to version level');
    } else {
      console.log('⚠️  Some documents still have document-level tags - manual review needed');
    }

  } catch (error) {
    console.error('Migration error:', error);
    throw error;
  }
};

// Run migration
const runMigration = async () => {
  try {
    await connectDB();
    await migrateTags();
    console.log('Migration completed');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

// Check if this script is being run directly
if (require.main === module) {
  runMigration();
}

module.exports = { migrateTags };
