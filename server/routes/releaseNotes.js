const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup, addGroupToData } = require('../middleware/groupAuth');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');
const Project = require('../models/Project');
const ReleaseNotesHistory = require('../models/ReleaseNotesHistory');

// Debug logging middleware for release notes routes
router.use((req, res, next) => {
  console.log('Release Notes route hit:', req.method, req.path);
  next();
});

// Get release data for a project with filtering
router.get('/projects/:projectId/release-data', auth, addGroupContext, async (req, res) => {
  try {
    const { tags, labels, features, states } = req.query;
    
    // Build base filter
    let featureFilter = { project: req.params.projectId };
    let requirementFilter = { project: req.params.projectId };

    // Add group filter for non-super users
    if (!req.isSuperUser) {
      featureFilter.group = req.userGroup._id;
      requirementFilter.group = req.userGroup._id;
    }

    // Apply filters
    if (tags) {
      const tagArray = Array.isArray(tags) ? tags : [tags];
      featureFilter['releaseTags.tag'] = { $in: tagArray };
      requirementFilter['releaseTags.tag'] = { $in: tagArray };
    }

    if (features) {
      const featureArray = Array.isArray(features) ? features : [features];
      featureFilter._id = { $in: featureArray };
      requirementFilter.feature = { $in: featureArray };
    }

    if (states) {
      const stateArray = Array.isArray(states) ? states : [states];
      featureFilter.state = { $in: stateArray };
      requirementFilter['versions.state'] = { $in: stateArray };
    }

    // Fetch features and requirements
    const featuresData = await Feature.find(featureFilter)
      .populate('createdBy', 'username firstName lastName')
      .populate('releaseTags.addedBy', 'username firstName lastName')
      .sort({ createdAt: -1 });

    const requirementsData = await Requirement.find(requirementFilter)
      .populate('createdBy', 'username firstName lastName')
      .populate('feature', 'title')
      .populate('releaseTags.addedBy', 'username firstName lastName')
      .sort({ createdAt: -1 });

    // Apply label filter if specified (needs to be done after fetch due to nested structure)
    let filteredFeatures = featuresData;
    let filteredRequirements = requirementsData;

    if (labels) {
      const labelArray = Array.isArray(labels) ? labels : [labels];
      filteredFeatures = featuresData.filter(feature => {
        const currentVersion = feature.versions[feature.versions.length - 1];
        return currentVersion.labels && currentVersion.labels.some(label => 
          labelArray.includes(label.toString())
        );
      });

      filteredRequirements = requirementsData.filter(requirement => {
        const currentVersion = requirement.versions[requirement.versions.length - 1];
        return currentVersion.labels && currentVersion.labels.some(label => 
          labelArray.includes(label.toString())
        );
      });
    }

    res.json({
      features: filteredFeatures,
      requirements: filteredRequirements
    });
  } catch (err) {
    console.error('Error fetching release data:', err);
    res.status(500).send('Server Error');
  }
});

// Save release notes generation record
router.post('/projects/:projectId/release-notes', auth, addGroupContext, async (req, res) => {
  try {
    const { title, filters } = req.body;
    
    if (!title || !title.trim()) {
      return res.status(400).json({ message: 'Title is required' });
    }

    const releaseNotesData = addGroupToData(req, {
      project: req.params.projectId,
      title: title.trim(),
      generatedBy: req.user.userId,
      filters: filters || {}
    });

    const releaseNotes = new ReleaseNotesHistory(releaseNotesData);
    const savedReleaseNotes = await releaseNotes.save();

    const populatedReleaseNotes = await ReleaseNotesHistory.findById(savedReleaseNotes._id)
      .populate('generatedBy', 'username firstName lastName')
      .populate('project', 'name description');

    res.json(populatedReleaseNotes);
  } catch (err) {
    console.error('Error saving release notes record:', err);
    res.status(500).send('Server Error');
  }
});

// Get historical release notes for a project
router.get('/projects/:projectId/release-notes', auth, addGroupContext, filterByGroup, async (req, res) => {
  try {
    let filter = { project: req.params.projectId };
    
    // Add group filter for non-super users
    if (!req.isSuperUser) {
      filter.group = req.userGroup._id;
    }

    const releaseNotes = await ReleaseNotesHistory.find(filter)
      .populate('generatedBy', 'username firstName lastName')
      .populate('project', 'name description')
      .sort({ generatedAt: -1 });

    res.json(releaseNotes);
  } catch (err) {
    console.error('Error fetching release notes history:', err);
    res.status(500).send('Server Error');
  }
});

// Get specific release notes data
router.get('/projects/:projectId/release-notes/:releaseNotesId', auth, addGroupContext, async (req, res) => {
  try {
    const releaseNotes = await ReleaseNotesHistory.findById(req.params.releaseNotesId)
      .populate('generatedBy', 'username firstName lastName')
      .populate('project', 'name description');

    if (!releaseNotes) {
      return res.status(404).json({ message: 'Release notes not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && releaseNotes.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(releaseNotes);
  } catch (err) {
    console.error('Error fetching specific release notes:', err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
