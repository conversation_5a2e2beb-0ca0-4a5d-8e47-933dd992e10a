import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControlLabel,
  Checkbox,
  Typography,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Assignment as RequirementIcon,
  Description as UserStoryIcon
} from '@mui/icons-material';
import axios from 'axios';

const BulkTagDialog = ({ 
  open, 
  onClose, 
  feature, 
  childRequirements = [],
  onSuccess 
}) => {
  const [tag, setTag] = useState('');
  const [applyToFeature, setApplyToFeature] = useState(true);
  const [selectedRequirements, setSelectedRequirements] = useState(new Set());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setTag('');
      setApplyToFeature(true);
      setSelectedRequirements(new Set());
      setError('');
    }
  }, [open]);

  const handleRequirementToggle = (requirementId) => {
    const newSelected = new Set(selectedRequirements);
    if (newSelected.has(requirementId)) {
      newSelected.delete(requirementId);
    } else {
      newSelected.add(requirementId);
    }
    setSelectedRequirements(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRequirements.size === childRequirements.length) {
      // Deselect all
      setSelectedRequirements(new Set());
    } else {
      // Select all
      setSelectedRequirements(new Set(childRequirements.map(req => req._id)));
    }
  };

  const handleApply = async () => {
    if (!tag.trim()) {
      setError('Tag is required');
      return;
    }

    if (!applyToFeature && selectedRequirements.size === 0) {
      setError('Please select at least one requirement or check "Apply to Feature"');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const response = await axios.post(`/api/features/${feature._id}/bulk-tag`, {
        tag: tag.trim(),
        childRequirementIds: Array.from(selectedRequirements),
        applyToFeature
      });

      if (onSuccess) {
        onSuccess(response.data);
      }

      onClose();
    } catch (err) {
      console.error('Error applying bulk tag:', err);
      setError(err.response?.data?.message || 'Failed to apply tag');
    } finally {
      setLoading(false);
    }
  };

  const getRequirementIcon = (type) => {
    switch (type) {
      case 'user_story':
        return <UserStoryIcon />;
      case 'requirement':
      default:
        return <RequirementIcon />;
    }
  };

  const getRequirementTypeLabel = (type) => {
    switch (type) {
      case 'user_story':
        return 'User Story';
      case 'requirement':
      default:
        return 'Requirement';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '400px' }
      }}
    >
      <DialogTitle>
        Apply Release Tag
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="Release Tag"
            placeholder="e.g., v1.0.0, Release-2024-01, Sprint-15"
            value={tag}
            onChange={(e) => setTag(e.target.value)}
            disabled={loading}
            autoFocus
          />
        </Box>

        <Box sx={{ mb: 3 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={applyToFeature}
                onChange={(e) => setApplyToFeature(e.target.checked)}
                disabled={loading}
              />
            }
            label={`Apply tag to feature: "${feature?.versions?.[feature.versions.length - 1]?.title || 'Untitled'}"`}
          />
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="h6">
              Child Requirements ({childRequirements.length})
            </Typography>
            {childRequirements.length > 0 && (
              <Button
                size="small"
                onClick={handleSelectAll}
                disabled={loading}
              >
                {selectedRequirements.size === childRequirements.length ? 'Deselect All' : 'Select All'}
              </Button>
            )}
          </Box>

          {childRequirements.length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', py: 2 }}>
              This feature has no child requirements.
            </Typography>
          ) : (
            <List sx={{ maxHeight: 300, overflow: 'auto' }}>
              {childRequirements.map((requirement) => {
                const currentVersion = requirement.versions[requirement.versions.length - 1];
                return (
                  <ListItem
                    key={requirement._id}
                    button
                    onClick={() => handleRequirementToggle(requirement._id)}
                    disabled={loading}
                    sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Checkbox
                        checked={selectedRequirements.has(requirement._id)}
                        onChange={() => handleRequirementToggle(requirement._id)}
                        disabled={loading}
                      />
                    </ListItemIcon>
                    <ListItemIcon>
                      {getRequirementIcon(requirement.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={currentVersion.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {getRequirementTypeLabel(requirement.type)} • State: {currentVersion.state}
                          </Typography>
                          {requirement.releaseTags && requirement.releaseTags.length > 0 && (
                            <Typography variant="body2" color="primary">
                              Existing tags: {requirement.releaseTags.map(t => t.tag).join(', ')}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          )}
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          Selected: {selectedRequirements.size} requirement{selectedRequirements.size !== 1 ? 's' : ''}
          {applyToFeature && ' + 1 feature'}
        </Typography>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handleApply} 
          variant="contained"
          disabled={loading || !tag.trim() || (!applyToFeature && selectedRequirements.size === 0)}
          startIcon={loading ? <CircularProgress size={16} /> : null}
        >
          Apply Tag
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkTagDialog;
